# -*- coding: utf-8 -*-
"""
刚体处理模块
"""

import numpy as np
from module.MMath import MVector3D, MVector4D, MQuaternion, MMatrix4x4
from mmd.PmxData import PmxModel, RigidBody, Joint
from utils.MLogger import MLogger
from .rigidbody_config import RIGIDBODY_PAIRS

logger = MLogger(__name__)


class RigidbodyProcessor:
    """刚体处理器"""
    
    def __init__(self):
        pass

    def _find_root_body_bone(self, bone, model):
        """追溯装饰骨骼到根主要身体骨骼"""
        main_body_bones = {
            "下半身", "上半身", "上半身2", "上半身3", "首", "頭",
            "左腕", "右腕", "左ひじ", "右ひじ", "左手首", "右手首",
            "左足", "右足", "左ひざ", "右ひざ", "左足首", "右足首",
            "左肩", "右肩", "左胸", "右胸"
        }

        current_bone = bone
        visited = set()  # 防止循环引用
        path = []  # 记录路径用于调试

        while current_bone and current_bone.name not in visited:
            visited.add(current_bone.name)
            path.append(current_bone.name)

            # 如果当前骨骼是主要身体骨骼，返回它
            if current_bone.name in main_body_bones:
                print(f"--     找到根ボーン路径: {' -> '.join(path)}")
                return current_bone

            # 追溯到父骨骼
            if current_bone.parent_index >= 0 and current_bone.parent_index in model.bone_indexes:
                parent_name = model.bone_indexes[current_bone.parent_index]
                if parent_name in model.bones:
                    current_bone = model.bones[parent_name]
                else:
                    break
            else:
                break

        # 如果没有找到主要身体骨骼，显示路径并检查最后一个骨骼的父骨骼
        if current_bone and current_bone.parent_index >= 0 and current_bone.parent_index in model.bone_indexes:
            final_parent = model.bone_indexes[current_bone.parent_index]
            print(f"--     根ボーン未找到，路径: {' -> '.join(path)} -> {final_parent}")
        else:
            print(f"--     根ボーン未找到，路径: {' -> '.join(path)} (无父骨骼)")
        return None

    def create_body_rigidbody(self, model: PmxModel):
        """创建身体刚体"""
        print("-- 身体剛体設定開始")
        logger.info("-- 身体剛体設定開始")

        skin_vidxs = []
        cloth_vidxs = []
        print(f"-- 材質総数: {len(model.material_vertices)}")
        for material_name, vidxs in model.material_vertices.items():
            material = model.materials[material_name]
            print(f"-- 材質チェック: {material_name}, english_name: {material.english_name}, 頂点数: {len(vidxs)}")
            # 原始代码中是检查材质名称，而不是english_name
            # 皮肤材质通常包含"Skin"或"Body"
            if "Skin" in material_name or "Body" in material_name or "_Face" in material_name:
                skin_vidxs.extend(vidxs)
                print(f"-- SKIN材質発見: {material_name}, 頂点数: {len(vidxs)}")
            elif "Cloth" in material_name or "Wear" in material_name or "Accessory" in material_name:
                cloth_vidxs.extend(vidxs)
                print(f"-- CLOTH材質発見: {material_name}, 頂点数: {len(vidxs)}")

        print(f"-- SKIN頂点数: {len(skin_vidxs)}, CLOTH頂点数: {len(cloth_vidxs)}")

        bone_vertices = {}
        bone_weights = {}

        # 主要身体骨骼列表
        main_body_bones = {
            "下半身", "上半身", "上半身2", "上半身3", "首", "頭",
            "左腕", "右腕", "左ひじ", "右ひじ", "左手首", "右手首",
            "左足", "右足", "左ひざ", "右ひざ", "左足首", "右足首",
            "左肩", "右肩", "左胸", "右胸"
        }

        for bidx, vidxs in model.vertices.items():
            # 骨骼索引边界检查
            if bidx not in model.bone_indexes:
                continue
            bone = model.bones[model.bone_indexes[bidx]]
            target_bone_weights = {}

            bone_strong_vidxs = [
                vidx for vidx in vidxs if bone.index in model.vertex_dict[vidx].deform.get_idx_list(0.4)
            ]
            target_bone_vidxs = list(set(skin_vidxs) & set(bone_strong_vidxs))

            if 20 > len(target_bone_vidxs):
                # 強参照頂点が少ない場合、弱参照頂点を確認する
                bone_weak_vidxs = [
                    vidx for vidx in vidxs if bone.index in model.vertex_dict[vidx].deform.get_idx_list(0.2)
                ]
                target_bone_vidxs = list(set(skin_vidxs) & set(bone_weak_vidxs))

            if 20 > len(target_bone_vidxs) or "足先EX" in bone.name:
                # 弱参照肌頂点が少ない場合、衣装強参照頂点を確認する
                # 足先は靴が必ず入るので衣装も含む
                target_bone_vidxs = list((set(skin_vidxs) | set(cloth_vidxs)) & set(bone_strong_vidxs))

            if 20 > len(target_bone_vidxs):
                # 衣装強参照頂点が少ない場合、衣装弱参照頂点を確認する
                bone_weak_vidxs = [
                    vidx for vidx in vidxs if bone.index in model.vertex_dict[vidx].deform.get_idx_list(0.2)
                ]
                target_bone_vidxs = list((set(skin_vidxs) | set(cloth_vidxs)) & set(bone_weak_vidxs))

            # 主要身体骨骼的详细调试信息
            if bone.name in main_body_bones:
                print(f"-- 主要ボーン[{bone.name}]: 全頂点数={len(vidxs)}, 強影響頂点数={len(bone_strong_vidxs)}, 最終対象頂点数={len(target_bone_vidxs)}")
                print(f"--   skin_vidxs数={len(skin_vidxs)}, cloth_vidxs数={len(cloth_vidxs)}")
                if len(target_bone_vidxs) < 20:
                    print(f"--   主要ボーン[{bone.name}]の頂点数が不足: {len(target_bone_vidxs)} < 20")

            logger.debug(f"-- ボーン[{bone.name}]: 全頂点数={len(vidxs)}, 強影響頂点数={len(bone_strong_vidxs)}, 最終対象頂点数={len(target_bone_vidxs)}")

            # 按照原始代码逻辑，如果顶点数少于20个就跳过
            if 20 > len(target_bone_vidxs):
                continue

            for vidx in target_bone_vidxs:
                vertex = model.vertex_dict[vidx]
                weight = vertex.deform.get_weight(bone.index)
                if weight > 0:
                    target_bone_weights[vidx] = weight

            # 骨骼重新分配逻辑（参考原始代码第587-605行）
            target_bones = []
            if "捩" in bone.name:
                # 捩りは親に入れる
                if bone.parent_index >= 0 and bone.parent_index in model.bone_indexes:
                    target_bones.append(model.bones[model.bone_indexes[bone.parent_index]])
                    print(f"--   捩りボーン -> 親ボーン: {model.bone_indexes[bone.parent_index]}")
            elif "指" in bone.name:
                # 指は手首に入れる
                wrist_name = f"{bone.name[0]}手首"
                if wrist_name in model.bones:
                    target_bones.append(model.bones[wrist_name])
                    print(f"--   指ボーン -> 手首ボーン: {wrist_name}")
            elif "胸先" in bone.name:
                # 胸先は胸に入れる
                chest_name = f"{bone.name[0]}胸"
                if chest_name in model.bones:
                    target_bones.append(model.bones[chest_name])
                    print(f"--   胸先ボーン -> 胸ボーン: {chest_name}")
            elif "胸" in bone.name:
                # 胸は胸と上半身2に入れる
                target_bones.append(bone)
                if "上半身2" in model.bones:
                    target_bones.append(model.bones["上半身2"])
                    print(f"--   胸ボーン -> 胸 + 上半身2")
            elif "足先EX" in bone.name:
                # 足先EXは足首に入れる
                ankle_name = f"{bone.name[0]}足首"
                if ankle_name in model.bones:
                    target_bones.append(model.bones[ankle_name])
                    print(f"--   足先EXボーン -> 足首ボーン: {ankle_name}")
            elif bone.getExternalRotationFlag() and bone.effect_factor == 1:
                # 回転付与の場合、付与親に入れる(足D系)
                if bone.effect_index >= 0 and bone.effect_index in model.bone_indexes:
                    target_bones.append(model.bones[model.bone_indexes[bone.effect_index]])
                    print(f"--   回転付与ボーン -> 付与親ボーン: {model.bone_indexes[bone.effect_index]}")
            else:
                # その他は自身に入れる
                target_bones.append(bone)

            # 各対象骨骼に顶点を分配
            for target_bone in target_bones:
                if target_bone.name not in bone_vertices:
                    bone_vertices[target_bone.name] = []
                    bone_weights[target_bone.name] = {}

                # 顶点を追加（重复を避ける）
                bone_vertices[target_bone.name].extend(target_bone_vidxs)
                for vidx, weight in target_bone_weights.items():
                    if vidx not in bone_weights[target_bone.name]:
                        bone_weights[target_bone.name][vidx] = 0
                    bone_weights[target_bone.name][vidx] += weight

            logger.debug(f"-- ボーン[{bone.name}]の剛体対象頂点数: {len(target_bone_vidxs)}")

        # 刚体生成
        print(f"-- 剛体対象ボーン数: {len(bone_vertices)}")
        print(f"-- 剛体設定数: {len(RIGIDBODY_PAIRS)}")
        print(f"-- bone_vertices中のボーン名: {list(bone_vertices.keys())[:10]}...")  # 前10个骨骼名
        print(f"-- bone_vertices中是否包含主要骨骼:")
        main_bones_check = ["下半身", "上半身", "上半身2", "上半身3", "首", "頭"]
        for bone_name in main_bones_check:
            if bone_name in bone_vertices:
                print(f"--   {bone_name}: {len(bone_vertices[bone_name])}個頂点")
            else:
                print(f"--   {bone_name}: 未包含")

        # 检查主要身体骨骼是否存在
        main_bones = ["下半身", "上半身", "上半身2", "上半身3", "首", "頭", "左腕", "右腕", "左足", "右足"]
        for bone_name in main_bones:
            if bone_name in model.bones:
                bone = model.bones[bone_name]
                vidxs = model.vertices.get(bone.index, [])
                print(f"-- 主要ボーン[{bone_name}]: 存在=True, index={bone.index}, 全頂点数={len(vidxs)}")
            else:
                print(f"-- 主要ボーン[{bone_name}]: 存在=False")

        # 检查model.vertices中实际有哪些骨骼索引
        print(f"-- model.vertices中的骨骼索引: {list(model.vertices.keys())[:10]}...")
        print(f"-- model.vertices总数: {len(model.vertices)}")

        # 检查骨骼索引映射
        print(f"-- model.bone_indexes总数: {len(model.bone_indexes)}")
        sample_indexes = list(model.bone_indexes.keys())[:5]
        for idx in sample_indexes:
            bone_name = model.bone_indexes.get(idx, "Unknown")
            print(f"-- 骨骼索引映射: {idx} -> {bone_name}")

        # 检查model.vertices中实际包含的骨骼名称
        print(f"-- model.vertices中实际包含的骨骼:")
        vertices_bone_names = []
        for bidx in model.vertices.keys():
            if bidx in model.bone_indexes:
                bone_name = model.bone_indexes[bidx]
                vertices_bone_names.append(bone_name)
        print(f"-- 前20个骨骼: {vertices_bone_names[:20]}")

        # 检查主要身体骨骼是否在model.vertices中
        main_body_bones = {
            "下半身", "上半身", "上半身2", "上半身3", "首", "頭",
            "左腕", "右腕", "左ひじ", "右ひじ", "左手首", "右手首",
            "左足", "右足", "左ひざ", "右ひざ", "左足首", "右足首",
            "左肩", "右肩", "左胸", "右胸"
        }
        print(f"-- 主要身体骨骼在model.vertices中的情况:")
        for bone_name in main_body_bones:
            if bone_name in model.bones:
                bone_index = model.bones[bone_name].index
                in_vertices = bone_index in model.vertices
                vertex_count = len(model.vertices.get(bone_index, []))
                print(f"--   {bone_name}: index={bone_index}, in_vertices={in_vertices}, vertex_count={vertex_count}")
            else:
                print(f"--   {bone_name}: 不存在")
        logger.info(f"-- 剛体対象ボーン数: {len(bone_vertices)}")
        for rigidbody_name, rigidbody_config in RIGIDBODY_PAIRS.items():
            bone_name = rigidbody_config["bone"]
            print(f"-- 剛体[{rigidbody_name}]処理開始: ボーン[{bone_name}]")

            if bone_name not in model.bones:
                print(f"-- 剛体[{rigidbody_name}]: ボーン[{bone_name}]が見つかりません")
                logger.debug(f"-- 剛体[{rigidbody_name}]: ボーン[{bone_name}]が見つかりません")
                continue

            if bone_name not in bone_vertices:
                print(f"-- 剛体[{rigidbody_name}]: ボーン[{bone_name}]に対象頂点がありません")
                logger.debug(f"-- 剛体[{rigidbody_name}]: ボーン[{bone_name}]に対象頂点がありません")
                continue

            bone = model.bones[bone_name]
            vidxs = bone_vertices[bone_name]
            weights = bone_weights[bone_name]

            # 刚体の位置とサイズを計算
            rigidbody_pos, rigidbody_size = self._calc_rigidbody_params(
                model, bone, vidxs, weights, rigidbody_config
            )

            # no_collision_groupの計算を原始コードに合わせる
            no_collision_group = 0
            for nc in range(16):
                if nc not in rigidbody_config["no_collision_group"]:
                    no_collision_group |= 1 << nc

            # 刚体作成
            rigidbody = RigidBody(
                name=rigidbody_name,
                english_name=rigidbody_config.get("english", rigidbody_name),
                bone_index=bone.index,
                collision_group=rigidbody_config["group"],
                no_collision_group=no_collision_group,
                shape_type=rigidbody_config["shape"],
                shape_size=rigidbody_size,
                shape_position=rigidbody_pos,
                shape_rotation=MVector3D(),
                mass=10.0,  # 原始コードに合わせる
                linear_damping=0.5,
                angular_damping=0.5,
                restitution=0.0,
                friction=0.0,  # 原始コードに合わせる
                mode=0  # ボーン追従
            )

            # インデックス設定（重要！）
            rigidbody.index = len(model.rigidbodies)
            model.rigidbodies[rigidbody_name] = rigidbody

            print(f"-- 剛体[{rigidbody_name}]作成完了: index={rigidbody.index}")
            logger.debug(f"-- 剛体[{rigidbody_name}]作成完了: index={rigidbody.index}")

        print(f"-- 身体剛体設定終了: 作成数={len(model.rigidbodies)}")
        logger.info(f"-- 身体剛体設定終了: 作成数={len(model.rigidbodies)}")
        return model

    def _calc_rigidbody_params(self, model, bone, vidxs, weights, config):
        """刚体参数计算 - 基于原始代码逻辑"""

        rigidbody_name = None
        for name, cfg in RIGIDBODY_PAIRS.items():
            if cfg == config:
                rigidbody_name = name
                break

        # ボーンの向き先に沿う
        if "手首" in bone.name:
            # 手首は中指3を方向とする
            finger_name = f"{bone.name[0]}中指先"
            if finger_name in model.bones:
                tail_position = model.bones[finger_name].position
            else:
                tail_position = bone.tail_position + bone.position
        else:
            if bone.tail_index > 0:
                tail_bones = [b for b in model.bones.values() if bone.tail_index == b.index]
                if tail_bones:
                    tail_position = tail_bones[0].position
                else:
                    tail_position = bone.tail_position + bone.position
            else:
                tail_position = bone.tail_position + bone.position

        if config.get("direction") == "horizontal":
            # ボーン進行方向(x)
            x_direction_pos = MVector3D(1, 0, 0)
            # ボーン進行方向に対しての横軸(y)
            y_direction_pos = MVector3D(0, 1, 0)
        else:
            # ボーン進行方向(x)
            x_direction_pos = (tail_position - bone.position).normalized()
            # ボーン進行方向に対しての横軸(y)
            y_direction_pos = MVector3D(1, 0, 0)
        # ボーン進行方向に対しての縦軸(z)
        z_direction_pos = MVector3D.crossProduct(x_direction_pos, y_direction_pos)
        bone_shape_qq = MQuaternion.fromDirection(z_direction_pos, x_direction_pos)

        # 頂点位置とウェイトを収集
        vposes = []
        vweights = []
        for vidx in vidxs:
            vposes.append(model.vertex_dict[vidx].position.data())
            vweights.append(weights[vidx])

        if not vposes:
            return bone.position, MVector3D(0.1, 0.1, 0.1)

        # 範囲による分割処理
        if config.get("range") in ["upper", "lower"]:
            # 重心
            gravity_pos = MVector3D(np.average(vposes, axis=0, weights=vweights))

            mat = MMatrix4x4()
            mat.setToIdentity()
            mat.translate(gravity_pos)
            mat.rotate(bone_shape_qq)

            # 上下に分ける系はローカル位置で分ける
            local_vposes = np.array([(mat.inverted() * MVector3D(vpos)).data() for vpos in vposes])

            # 中央値
            mean_y = np.mean(local_vposes, axis=0)[1]

            target_vposes = []
            target_vweights = []
            for vpos, vweight in zip(local_vposes, vweights):
                if (vpos[1] >= mean_y and config.get("range") == "upper") or (
                    vpos[1] <= mean_y and config.get("range") == "lower"
                ):
                    target_vposes.append((mat * MVector3D(vpos)).data())
                    target_vweights.append(vweight)
        else:
            target_vposes = vposes
            target_vweights = vweights

        # 重心
        shape_position = MVector3D(np.average(target_vposes, axis=0, weights=target_vweights))

        mat = MMatrix4x4()
        mat.setToIdentity()
        mat.translate(shape_position)
        mat.rotate(bone_shape_qq)

        target_local_vposes = np.array([(mat.inverted() * MVector3D(vpos)).data() for vpos in target_vposes])

        local_vpos_diff = np.max(target_local_vposes, axis=0) - np.min(target_local_vposes, axis=0)

        if config.get("shape") == 0:
            x_size = y_size = np.mean(local_vpos_diff) / 2
        else:
            x_size = np.mean(local_vpos_diff[0::2]) / 2
            y_size = local_vpos_diff[1] - x_size * 0.7

        if rigidbody_name in ["上半身2", "上半身3"]:
            # ちょっと後ろにずらす
            shape_position.setZ(shape_position.z() + (x_size * 0.5))

        shape_size = MVector3D(x_size, y_size, x_size) * config.get("ratio", MVector3D(1, 1, 1))

        if config.get("shape") == 0:
            # 球剛体はバウンティングボックスの中心
            shape_position = MVector3D(
                np.mean([np.max(target_vposes, axis=0), np.min(target_vposes, axis=0)], axis=0)
            )
            if rigidbody_name and "尻" in rigidbody_name:
                if "下半身" in model.bones and f"{rigidbody_name[0]}足" in model.bones:
                    shape_position = MVector3D(
                        np.average(
                            [model.bones["下半身"].position.data(), model.bones[f"{rigidbody_name[0]}足"].position.data()],
                            axis=0,
                            weights=[0.3, 0.7],
                        )
                    )
            elif rigidbody_name and "胸" in rigidbody_name:
                chest_bone_name = f"{rigidbody_name[0]}胸"
                if chest_bone_name in model.bones:
                    shape_position = MVector3D(
                        np.average(
                            [shape_position.data(), model.bones[chest_bone_name].position.data()],
                            axis=0,
                            weights=[0.3, 0.7],
                        )
                    )
            elif rigidbody_name and "後頭部" in rigidbody_name:
                shape_position = MVector3D(np.average(target_vposes, axis=0, weights=target_vweights))
                shape_position.setZ(shape_position.z() + (x_size * 0.3))

        # 最小サイズ制限
        min_size = 0.1
        shape_size.setX(max(shape_size.x(), min_size))
        shape_size.setY(max(shape_size.y(), min_size))
        shape_size.setZ(max(shape_size.z(), min_size))

        return shape_position, shape_size

    def create_joints(self, model: PmxModel):
        """ジョイント作成"""
        # 基本的なジョイント設定
        joint_configs = [
            {
                "name": "首ジョイント",
                "rigidbody_a": "首",
                "rigidbody_b": "頭",
                "position": None,  # 自動計算
                "rotation": MVector3D(),
                "translation_limit_min": MVector3D(-0.5, -0.5, -0.5),
                "translation_limit_max": MVector3D(0.5, 0.5, 0.5),
                "rotation_limit_min": MVector3D(-30, -30, -30),
                "rotation_limit_max": MVector3D(30, 30, 30),
            },
            {
                "name": "左肩ジョイント",
                "rigidbody_a": "上半身3",
                "rigidbody_b": "左肩",
                "position": None,
                "rotation": MVector3D(),
                "translation_limit_min": MVector3D(-0.2, -0.2, -0.2),
                "translation_limit_max": MVector3D(0.2, 0.2, 0.2),
                "rotation_limit_min": MVector3D(-45, -45, -45),
                "rotation_limit_max": MVector3D(45, 45, 45),
            },
            {
                "name": "右肩ジョイント",
                "rigidbody_a": "上半身3",
                "rigidbody_b": "右肩",
                "position": None,
                "rotation": MVector3D(),
                "translation_limit_min": MVector3D(-0.2, -0.2, -0.2),
                "translation_limit_max": MVector3D(0.2, 0.2, 0.2),
                "rotation_limit_min": MVector3D(-45, -45, -45),
                "rotation_limit_max": MVector3D(45, 45, 45),
            },
        ]

        for joint_config in joint_configs:
            rigidbody_a_name = joint_config["rigidbody_a"]
            rigidbody_b_name = joint_config["rigidbody_b"]
            
            if rigidbody_a_name in model.rigidbodies and rigidbody_b_name in model.rigidbodies:
                rigidbody_a = model.rigidbodies[rigidbody_a_name]
                rigidbody_b = model.rigidbodies[rigidbody_b_name]
                
                # ジョイント位置を自動計算
                if joint_config["position"] is None:
                    joint_position = (rigidbody_a.position + rigidbody_b.position) * 0.5
                else:
                    joint_position = joint_config["position"]

                joint = Joint(
                    name=joint_config["name"],
                    english_name=joint_config["name"],
                    joint_type=0,  # スプリング6DOF
                    rigidbody_index_a=rigidbody_a.index,
                    rigidbody_index_b=rigidbody_b.index,
                    position=joint_position,
                    rotation=joint_config["rotation"],
                    translation_limit_min=joint_config["translation_limit_min"],
                    translation_limit_max=joint_config["translation_limit_max"],
                    rotation_limit_min=joint_config["rotation_limit_min"],
                    rotation_limit_max=joint_config["rotation_limit_max"],
                    spring_constant_translation=MVector3D(1000, 1000, 1000),
                    spring_constant_rotation=MVector3D(100, 100, 100)
                )

                model.joints[joint.name] = joint

        return model
