# -*- coding: utf-8 -*-
"""
刚体处理模块
"""

import numpy as np
from module.MMath import MVector3D, MVector4D, MQuaternion
from mmd.PmxData import PmxModel, RigidBody, Joint
from utils.MLogger import <PERSON><PERSON>ogger
from .rigidbody_config import RIGIDBODY_PAIRS
from .vroid_utils import calc_ratio

logger = MLogger(__name__)


class RigidbodyProcessor:
    """刚体处理器"""
    
    def __init__(self):
        pass

    def _find_root_body_bone(self, bone, model):
        """追溯装饰骨骼到根主要身体骨骼"""
        main_body_bones = {
            "下半身", "上半身", "上半身2", "上半身3", "首", "頭",
            "左腕", "右腕", "左ひじ", "右ひじ", "左手首", "右手首",
            "左足", "右足", "左ひざ", "右ひざ", "左足首", "右足首",
            "左肩", "右肩", "左胸", "右胸"
        }

        current_bone = bone
        visited = set()  # 防止循环引用
        path = []  # 记录路径用于调试

        while current_bone and current_bone.name not in visited:
            visited.add(current_bone.name)
            path.append(current_bone.name)

            # 如果当前骨骼是主要身体骨骼，返回它
            if current_bone.name in main_body_bones:
                print(f"--     找到根ボーン路径: {' -> '.join(path)}")
                return current_bone

            # 追溯到父骨骼
            if current_bone.parent_index >= 0 and current_bone.parent_index in model.bone_indexes:
                parent_name = model.bone_indexes[current_bone.parent_index]
                if parent_name in model.bones:
                    current_bone = model.bones[parent_name]
                else:
                    break
            else:
                break

        # 如果没有找到主要身体骨骼，显示路径并检查最后一个骨骼的父骨骼
        if current_bone and current_bone.parent_index >= 0 and current_bone.parent_index in model.bone_indexes:
            final_parent = model.bone_indexes[current_bone.parent_index]
            print(f"--     根ボーン未找到，路径: {' -> '.join(path)} -> {final_parent}")
        else:
            print(f"--     根ボーン未找到，路径: {' -> '.join(path)} (无父骨骼)")
        return None

    def create_body_rigidbody(self, model: PmxModel):
        """创建身体刚体"""
        print("-- 身体剛体設定開始")
        logger.info("-- 身体剛体設定開始")

        skin_vidxs = []
        cloth_vidxs = []
        print(f"-- 材質総数: {len(model.material_vertices)}")
        for material_name, vidxs in model.material_vertices.items():
            material = model.materials[material_name]
            print(f"-- 材質チェック: {material_name}, english_name: {material.english_name}, 頂点数: {len(vidxs)}")
            # 原始代码中是检查材质名称，而不是english_name
            # 皮肤材质通常包含"Skin"或"Body"
            if "Skin" in material_name or "Body" in material_name or "_Face" in material_name:
                skin_vidxs.extend(vidxs)
                print(f"-- SKIN材質発見: {material_name}, 頂点数: {len(vidxs)}")
            elif "Cloth" in material_name or "Wear" in material_name or "Accessory" in material_name:
                cloth_vidxs.extend(vidxs)
                print(f"-- CLOTH材質発見: {material_name}, 頂点数: {len(vidxs)}")

        print(f"-- SKIN頂点数: {len(skin_vidxs)}, CLOTH頂点数: {len(cloth_vidxs)}")

        bone_vertices = {}
        bone_weights = {}
        for bidx, vidxs in model.vertices.items():
            # 骨骼索引边界检查
            if bidx not in model.bone_indexes:
                continue
            bone = model.bones[model.bone_indexes[bidx]]
            target_bone_weights = {}

            bone_strong_vidxs = [
                vidx for vidx in vidxs if bone.index in model.vertex_dict[vidx].deform.get_idx_list(0.4)
            ]
            target_bone_vidxs = list(set(skin_vidxs) & set(bone_strong_vidxs))

            if 20 > len(target_bone_vidxs):
                # 強参照頂点が少ない場合、弱参照頂点を確認する
                bone_weak_vidxs = [
                    vidx for vidx in vidxs if bone.index in model.vertex_dict[vidx].deform.get_idx_list(0.2)
                ]
                target_bone_vidxs = list(set(skin_vidxs) & set(bone_weak_vidxs))

            if 20 > len(target_bone_vidxs) or "足先EX" in bone.name:
                # 弱参照肌頂点が少ない場合、衣装強参照頂点を確認する
                # 足先は靴が必ず入るので衣装も含む
                target_bone_vidxs = list((set(skin_vidxs) | set(cloth_vidxs)) & set(bone_strong_vidxs))

            if 20 > len(target_bone_vidxs):
                # 衣装強参照頂点が少ない場合、衣装弱参照頂点を確認する
                bone_weak_vidxs = [
                    vidx for vidx in vidxs if bone.index in model.vertex_dict[vidx].deform.get_idx_list(0.2)
                ]
                target_bone_vidxs = list((set(skin_vidxs) | set(cloth_vidxs)) & set(bone_weak_vidxs))

            logger.debug(f"-- ボーン[{bone.name}]: 全頂点数={len(vidxs)}, 強影響頂点数={len(bone_strong_vidxs)}, 最終対象頂点数={len(target_bone_vidxs)}")

            # 为了确保装饰骨骼的顶点被重新分配，我们不跳过顶点数少的骨骼
            # 而是直接处理所有有顶点的骨骼
            if len(target_bone_vidxs) == 0:
                # 如果没有皮肤/衣装顶点，但有其他顶点，也尝试处理
                if len(vidxs) > 0:
                    print(f"-- ボーン[{bone.name}]: 皮肤/衣装頂点=0, 但有{len(vidxs)}個其他頂点，尝试处理")
                    target_bone_vidxs = vidxs[:min(len(vidxs), 100)]  # 限制数量避免过多
                else:
                    continue

            for vidx in target_bone_vidxs:
                vertex = model.vertex_dict[vidx]
                weight = vertex.deform.get_weight(bone.index)
                if weight > 0:
                    target_bone_weights[vidx] = weight

            if target_bone_weights:
                print(f"-- 重新分配処理開始: ボーン[{bone.name}], 頂点数={len(target_bone_vidxs)}")

                # 骨骼重新分配逻辑（参考原始代码第587-605行）
                target_bones = []
                if "捩" in bone.name:
                    # 捩りは親に入れる
                    if bone.parent_index >= 0 and bone.parent_index in model.bone_indexes:
                        target_bones.append(model.bones[model.bone_indexes[bone.parent_index]])
                        print(f"--   捩りボーン -> 親ボーン: {model.bone_indexes[bone.parent_index]}")
                elif "指" in bone.name:
                    # 指は手首に入れる
                    wrist_name = f"{bone.name[0]}手首"
                    if wrist_name in model.bones:
                        target_bones.append(model.bones[wrist_name])
                        print(f"--   指ボーン -> 手首ボーン: {wrist_name}")
                elif "胸先" in bone.name:
                    # 胸先は胸に入れる
                    chest_name = f"{bone.name[0]}胸"
                    if chest_name in model.bones:
                        target_bones.append(model.bones[chest_name])
                        print(f"--   胸先ボーン -> 胸ボーン: {chest_name}")
                elif "胸" in bone.name:
                    # 胸は胸と上半身2に入れる
                    target_bones.append(bone)
                    if "上半身2" in model.bones:
                        target_bones.append(model.bones["上半身2"])
                        print(f"--   胸ボーン -> 胸 + 上半身2")
                elif "足先EX" in bone.name:
                    # 足先EXは足首に入れる
                    ankle_name = f"{bone.name[0]}足首"
                    if ankle_name in model.bones:
                        target_bones.append(model.bones[ankle_name])
                        print(f"--   足先EXボーン -> 足首ボーン: {ankle_name}")
                elif bone.getExternalRotationFlag() and bone.effect_factor == 1:
                    # 回転付与の場合、付与親に入れる(足D系)
                    if bone.effect_index >= 0 and bone.effect_index in model.bone_indexes:
                        target_bones.append(model.bones[model.bone_indexes[bone.effect_index]])
                        print(f"--   回転付与ボーン -> 付与親ボーン: {model.bone_indexes[bone.effect_index]}")
                else:
                    # 装飾骨骼和头发骨骼重新分配给主要身体骨骼
                    if "装飾_" in bone.name:
                        # 装饰骨骼分配给上半身（大多数装饰品在上半身）
                        if "上半身" in model.bones:
                            target_bones.append(model.bones["上半身"])
                            print(f"--   装飾ボーン -> 上半身: {bone.name} -> 上半身")
                        else:
                            target_bones.append(bone)
                            print(f"--   装飾ボーン -> 自身(上半身未找到): {bone.name}")
                    elif "髪_" in bone.name:
                        # 头发骨骼分配给头部
                        if "頭" in model.bones:
                            target_bones.append(model.bones["頭"])
                            print(f"--   髪ボーン -> 頭: {bone.name} -> 頭")
                        else:
                            target_bones.append(bone)
                            print(f"--   髪ボーン -> 自身(頭未找到): {bone.name}")
                    else:
                        # その他は自身に入れる
                        target_bones.append(bone)
                        print(f"--   その他ボーン -> 自身: {bone.name}")

                # 各対象骨骼に顶点を分配
                for target_bone in target_bones:
                    if target_bone.name not in bone_vertices:
                        bone_vertices[target_bone.name] = []
                        bone_weights[target_bone.name] = {}

                    # 顶点を追加（重复を避ける）
                    for vidx in target_bone_vidxs:
                        if vidx not in bone_vertices[target_bone.name]:
                            bone_vertices[target_bone.name].append(vidx)
                        if vidx not in bone_weights[target_bone.name]:
                            bone_weights[target_bone.name][vidx] = target_bone_weights[vidx]

                    print(f"--   分配完了: {target_bone.name} に {len(target_bone_vidxs)}個頂点追加")

                logger.debug(f"-- ボーン[{bone.name}]の剛体対象頂点数: {len(target_bone_vidxs)}")

        # 刚体生成
        print(f"-- 剛体対象ボーン数: {len(bone_vertices)}")
        print(f"-- 剛体設定数: {len(RIGIDBODY_PAIRS)}")
        print(f"-- bone_vertices中のボーン名: {list(bone_vertices.keys())[:10]}...")  # 前10个骨骼名
        print(f"-- bone_vertices中是否包含主要骨骼:")
        main_bones_check = ["下半身", "上半身", "上半身2", "上半身3", "首", "頭"]
        for bone_name in main_bones_check:
            if bone_name in bone_vertices:
                print(f"--   {bone_name}: {len(bone_vertices[bone_name])}個頂点")
            else:
                print(f"--   {bone_name}: 未包含")

        # 检查主要身体骨骼是否存在
        main_bones = ["下半身", "上半身", "上半身2", "上半身3", "首", "頭", "左腕", "右腕", "左足", "右足"]
        for bone_name in main_bones:
            if bone_name in model.bones:
                bone = model.bones[bone_name]
                vidxs = model.vertices.get(bone.index, [])
                print(f"-- 主要ボーン[{bone_name}]: 存在=True, index={bone.index}, 全頂点数={len(vidxs)}")
            else:
                print(f"-- 主要ボーン[{bone_name}]: 存在=False")

        # 检查model.vertices中实际有哪些骨骼索引
        print(f"-- model.vertices中的骨骼索引: {list(model.vertices.keys())[:10]}...")
        print(f"-- model.vertices总数: {len(model.vertices)}")

        # 检查骨骼索引映射
        print(f"-- model.bone_indexes总数: {len(model.bone_indexes)}")
        sample_indexes = list(model.bone_indexes.keys())[:5]
        for idx in sample_indexes:
            bone_name = model.bone_indexes.get(idx, "Unknown")
            print(f"-- 骨骼索引映射: {idx} -> {bone_name}")
        logger.info(f"-- 剛体対象ボーン数: {len(bone_vertices)}")
        for rigidbody_name, rigidbody_config in RIGIDBODY_PAIRS.items():
            bone_name = rigidbody_config["bone"]
            print(f"-- 剛体[{rigidbody_name}]処理開始: ボーン[{bone_name}]")

            if bone_name not in model.bones:
                print(f"-- 剛体[{rigidbody_name}]: ボーン[{bone_name}]が見つかりません")
                logger.debug(f"-- 剛体[{rigidbody_name}]: ボーン[{bone_name}]が見つかりません")
                continue

            if bone_name not in bone_vertices:
                print(f"-- 剛体[{rigidbody_name}]: ボーン[{bone_name}]に対象頂点がありません")
                logger.debug(f"-- 剛体[{rigidbody_name}]: ボーン[{bone_name}]に対象頂点がありません")
                continue

            bone = model.bones[bone_name]
            vidxs = bone_vertices[bone_name]
            weights = bone_weights[bone_name]

            # 刚体の位置とサイズを計算
            rigidbody_pos, rigidbody_size = self._calc_rigidbody_params(
                model, bone, vidxs, weights, rigidbody_config
            )

            # no_collision_groupの計算を原始コードに合わせる
            no_collision_group = 0
            for nc in range(16):
                if nc not in rigidbody_config["no_collision_group"]:
                    no_collision_group |= 1 << nc

            # 刚体作成
            rigidbody = RigidBody(
                name=rigidbody_name,
                english_name=rigidbody_config.get("english", rigidbody_name),
                bone_index=bone.index,
                collision_group=rigidbody_config["group"],
                no_collision_group=no_collision_group,
                shape_type=rigidbody_config["shape"],
                shape_size=rigidbody_size,
                shape_position=rigidbody_pos,
                shape_rotation=MVector3D(),
                mass=10.0,  # 原始コードに合わせる
                linear_damping=0.5,
                angular_damping=0.5,
                restitution=0.0,
                friction=0.0,  # 原始コードに合わせる
                mode=0  # ボーン追従
            )

            # インデックス設定（重要！）
            rigidbody.index = len(model.rigidbodies)
            model.rigidbodies[rigidbody_name] = rigidbody

            print(f"-- 剛体[{rigidbody_name}]作成完了: index={rigidbody.index}")
            logger.debug(f"-- 剛体[{rigidbody_name}]作成完了: index={rigidbody.index}")

        print(f"-- 身体剛体設定終了: 作成数={len(model.rigidbodies)}")
        logger.info(f"-- 身体剛体設定終了: 作成数={len(model.rigidbodies)}")
        return model

    def _calc_rigidbody_params(self, model, bone, vidxs, weights, config):
        """刚体参数计算"""
        # 頂点の重心を計算
        weighted_positions = []
        total_weight = 0
        
        for vidx in vidxs:
            vertex = model.vertex_dict[vidx]
            weight = weights[vidx]
            weighted_positions.append(vertex.position * weight)
            total_weight += weight
        
        if total_weight == 0:
            center_pos = bone.position
        else:
            center_pos = sum(weighted_positions, MVector3D()) / total_weight

        # 範囲を計算
        min_pos = MVector3D(float('inf'), float('inf'), float('inf'))
        max_pos = MVector3D(float('-inf'), float('-inf'), float('-inf'))
        
        for vidx in vidxs:
            vertex = model.vertex_dict[vidx]
            pos = vertex.position
            
            min_pos.setX(min(min_pos.x(), pos.x()))
            min_pos.setY(min(min_pos.y(), pos.y()))
            min_pos.setZ(min(min_pos.z(), pos.z()))

            max_pos.setX(max(max_pos.x(), pos.x()))
            max_pos.setY(max(max_pos.y(), pos.y()))
            max_pos.setZ(max(max_pos.z(), pos.z()))

        # サイズ計算
        size = max_pos - min_pos
        
        # 方向による調整
        direction = config.get("direction", "vertical")
        range_type = config.get("range", "all")
        
        if direction == "horizontal":
            # 水平方向を重視
            size = MVector3D(size.x(), size.y() * 0.5, size.z())
        elif direction == "vertical":
            # 垂直方向を重視
            size = MVector3D(size.x() * 0.5, size.y(), size.z() * 0.5)
        elif direction == "reverse":
            # 逆方向
            size = MVector3D(size.x() * 0.5, size.y() * 0.5, size.z())

        # 範囲による調整
        if range_type == "upper":
            center_pos.setY(center_pos.y() + size.y() * 0.25)
            size.setY(size.y() * 0.5)
        elif range_type == "lower":
            center_pos.setY(center_pos.y() - size.y() * 0.25)
            size.setY(size.y() * 0.5)

        # 比率による調整
        if "ratio" in config:
            ratio = config["ratio"]
            size = MVector3D(size.x() * ratio.x(), size.y() * ratio.y(), size.z() * ratio.z())

        # 最小サイズ制限
        min_size = 0.1
        size.setX(max(size.x(), min_size))
        size.setY(max(size.y(), min_size))
        size.setZ(max(size.z(), min_size))

        return center_pos, size

    def create_joints(self, model: PmxModel):
        """ジョイント作成"""
        # 基本的なジョイント設定
        joint_configs = [
            {
                "name": "首ジョイント",
                "rigidbody_a": "首",
                "rigidbody_b": "頭",
                "position": None,  # 自動計算
                "rotation": MVector3D(),
                "translation_limit_min": MVector3D(-0.5, -0.5, -0.5),
                "translation_limit_max": MVector3D(0.5, 0.5, 0.5),
                "rotation_limit_min": MVector3D(-30, -30, -30),
                "rotation_limit_max": MVector3D(30, 30, 30),
            },
            {
                "name": "左肩ジョイント",
                "rigidbody_a": "上半身3",
                "rigidbody_b": "左肩",
                "position": None,
                "rotation": MVector3D(),
                "translation_limit_min": MVector3D(-0.2, -0.2, -0.2),
                "translation_limit_max": MVector3D(0.2, 0.2, 0.2),
                "rotation_limit_min": MVector3D(-45, -45, -45),
                "rotation_limit_max": MVector3D(45, 45, 45),
            },
            {
                "name": "右肩ジョイント",
                "rigidbody_a": "上半身3",
                "rigidbody_b": "右肩",
                "position": None,
                "rotation": MVector3D(),
                "translation_limit_min": MVector3D(-0.2, -0.2, -0.2),
                "translation_limit_max": MVector3D(0.2, 0.2, 0.2),
                "rotation_limit_min": MVector3D(-45, -45, -45),
                "rotation_limit_max": MVector3D(45, 45, 45),
            },
        ]

        for joint_config in joint_configs:
            rigidbody_a_name = joint_config["rigidbody_a"]
            rigidbody_b_name = joint_config["rigidbody_b"]
            
            if rigidbody_a_name in model.rigidbodies and rigidbody_b_name in model.rigidbodies:
                rigidbody_a = model.rigidbodies[rigidbody_a_name]
                rigidbody_b = model.rigidbodies[rigidbody_b_name]
                
                # ジョイント位置を自動計算
                if joint_config["position"] is None:
                    joint_position = (rigidbody_a.position + rigidbody_b.position) * 0.5
                else:
                    joint_position = joint_config["position"]

                joint = Joint(
                    name=joint_config["name"],
                    english_name=joint_config["name"],
                    joint_type=0,  # スプリング6DOF
                    rigidbody_index_a=rigidbody_a.index,
                    rigidbody_index_b=rigidbody_b.index,
                    position=joint_position,
                    rotation=joint_config["rotation"],
                    translation_limit_min=joint_config["translation_limit_min"],
                    translation_limit_max=joint_config["translation_limit_max"],
                    rotation_limit_min=joint_config["rotation_limit_min"],
                    rotation_limit_max=joint_config["rotation_limit_max"],
                    spring_constant_translation=MVector3D(1000, 1000, 1000),
                    spring_constant_rotation=MVector3D(100, 100, 100)
                )

                model.joints[joint.name] = joint

        return model
